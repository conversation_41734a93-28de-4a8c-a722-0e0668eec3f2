#!/usr/bin/env tsx

/**
 * Debug script to examine the contents of Sims 4 package files
 * This will help us understand what resources are actually available for thumbnail extraction
 */

import * as fs from 'fs';
import * as path from 'path';
import { Package } from '@s4tk/models';
import { BinaryResourceType } from '@s4tk/models/enums';

// Path to a real mod file for testing
const MODS_PATH = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';

async function debugPackageContents() {
    console.log('🔍 Debugging Sims 4 Package Contents...\n');
    
    try {
        // Find .package files in the mods directory
        const packageFiles = findPackageFiles(MODS_PATH);

        if (packageFiles.length === 0) {
            console.log('❌ No .package files found in mods directory');
            return;
        }

        console.log(`📁 Found ${packageFiles.length} package files total`);

        // Look specifically for CC mods (Felixandre, build/buy content)
        const ccFiles = packageFiles.filter(file => {
            const fileName = path.basename(file).toLowerCase();
            return fileName.includes('felixandre') ||
                   fileName.includes('chateau') ||
                   fileName.includes('build') ||
                   fileName.includes('buy') ||
                   fileName.includes('furniture') ||
                   fileName.includes('cas') ||
                   fileName.includes('hair') ||
                   fileName.includes('clothing');
        });

        console.log(`🎨 Found ${ccFiles.length} potential CC files`);

        // Analyze CC files first, then some regular files
        const filesToAnalyze = [...ccFiles.slice(0, 3), ...packageFiles.slice(0, 2)];
        
        for (const filePath of filesToAnalyze) {
            await analyzePackageFile(filePath);
            console.log('\n' + '='.repeat(80) + '\n');
        }
        
    } catch (error) {
        console.error('❌ Error during analysis:', error);
    }
}

function findPackageFiles(directory: string): string[] {
    const packageFiles: string[] = [];
    
    try {
        const items = fs.readdirSync(directory);
        
        for (const item of items) {
            const fullPath = path.join(directory, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                // Recursively search subdirectories
                packageFiles.push(...findPackageFiles(fullPath));
            } else if (item.toLowerCase().endsWith('.package')) {
                packageFiles.push(fullPath);
            }
        }
    } catch (error) {
        console.warn(`⚠️ Could not read directory ${directory}:`, error.message);
    }
    
    return packageFiles;
}

async function analyzePackageFile(filePath: string) {
    console.log(`📦 Analyzing: ${path.basename(filePath)}`);
    console.log(`📍 Path: ${filePath}`);
    
    try {
        // Read the package file
        const buffer = fs.readFileSync(filePath);
        console.log(`📏 File size: ${(buffer.length / 1024).toFixed(2)} KB`);
        
        // Parse with S4TK
        const pkg = Package.from(buffer);
        console.log(`🔢 Total resources: ${pkg.size}`);
        
        // Analyze resource types
        const resourceTypes = new Map<number, number>();
        const imageResources: any[] = [];

        // Iterate through package entries correctly
        for (const entry of pkg.entries.values()) {
            const type = entry.key.type;
            resourceTypes.set(type, (resourceTypes.get(type) || 0) + 1);

            // Check for image/thumbnail resources
            if (isImageResource(type)) {
                imageResources.push({
                    type,
                    typeName: getResourceTypeName(type),
                    key: entry.key,
                    size: entry.value?.length || 0
                });
            }
        }
        
        // Display resource type summary
        console.log('\n📊 Resource Types Found:');
        const sortedTypes = Array.from(resourceTypes.entries())
            .sort((a, b) => b[1] - a[1]); // Sort by count descending
            
        for (const [type, count] of sortedTypes) {
            const typeName = getResourceTypeName(type);
            console.log(`  ${typeName} (0x${type.toString(16).toUpperCase()}): ${count}`);
        }
        
        // Display image resources specifically
        console.log('\n🖼️ Image/Thumbnail Resources:');
        if (imageResources.length === 0) {
            console.log('  ❌ No image resources found');
        } else {
            for (const img of imageResources) {
                console.log(`  ✅ ${img.typeName}: ${(img.size / 1024).toFixed(2)} KB`);
                console.log(`     Key: ${img.key.type.toString(16)}-${img.key.group.toString(16)}-${img.key.instance.toString(16)}`);
            }
        }
        
    } catch (error) {
        console.error(`❌ Failed to analyze ${path.basename(filePath)}:`, error.message);
    }
}

function isImageResource(type: number): boolean {
    return [
        BinaryResourceType.DdsImage,
        BinaryResourceType.DstImage,
        BinaryResourceType.PngImage,
        BinaryResourceType.RlesImage,
        BinaryResourceType.Rle2Image,
        BinaryResourceType.CasPartThumbnail
    ].includes(type);
}

function getResourceTypeName(type: number): string {
    const typeNames: Record<number, string> = {
        [BinaryResourceType.DdsImage]: 'DDS Image',
        [BinaryResourceType.DstImage]: 'DST Image', 
        [BinaryResourceType.PngImage]: 'PNG Image',
        [BinaryResourceType.RlesImage]: 'RLES Image',
        [BinaryResourceType.Rle2Image]: 'RLE2 Image',
        [BinaryResourceType.CasPartThumbnail]: 'CAS Thumbnail',
        [BinaryResourceType.CasPart]: 'CAS Part',
        [BinaryResourceType.ObjectDefinition]: 'Object Definition',
        [BinaryResourceType.SimData]: 'Sim Data',
        [BinaryResourceType.Model]: '3D Model',
        [BinaryResourceType.Tuning]: 'Tuning',
        [BinaryResourceType.CombinedTuning]: 'Combined Tuning',
        [BinaryResourceType.StringTable]: 'String Table',
        [BinaryResourceType.Animation]: 'Animation',
        [BinaryResourceType.Audio]: 'Audio',
        [BinaryResourceType.Script]: 'Script',
        [BinaryResourceType.PythonBytecode]: 'Python Bytecode'
    };
    
    return typeNames[type] || `Unknown (0x${type.toString(16).toUpperCase()})`;
}

// Run the debug script
if (require.main === module) {
    debugPackageContents();
}
