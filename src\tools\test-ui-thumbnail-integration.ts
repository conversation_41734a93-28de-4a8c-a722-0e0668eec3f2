#!/usr/bin/env tsx

/**
 * Test script to verify UI thumbnail integration
 * This simulates what the ModDashboard should be doing
 */

import * as fs from 'fs';
import * as path from 'path';
import { ThumbnailExtractionService } from '../services/visual/ThumbnailExtractionService';

// Mock mod data structure like what the UI uses
interface MockMod {
    fileName: string;
    filePath: string;
    thumbnailUrl?: string;
    thumbnailData?: string;
    category?: string;
    author?: string;
}

const MODS_PATH = 'C:\\Users\\<USER>\\Documents\\Electronic Arts\\The Sims 4\\Mods';

async function testUIThumbnailIntegration() {
    console.log('🧪 Testing UI Thumbnail Integration...\n');
    
    try {
        // Find some CC mod files
        const ccFiles = findCCFiles(MODS_PATH).slice(0, 3); // Test with 3 files
        
        if (ccFiles.length === 0) {
            console.log('❌ No CC files found for testing');
            return;
        }
        
        // Create mock mod objects like the UI would have
        const mods: MockMod[] = ccFiles.map(filePath => ({
            fileName: path.basename(filePath),
            filePath: filePath,
            category: 'Custom Content',
            author: 'Unknown'
        }));
        
        console.log(`📦 Testing with ${mods.length} mods:`);
        mods.forEach(mod => console.log(`  - ${mod.fileName}`));
        
        // Simulate the ModDashboard extractThumbnails function
        console.log('\n🎨 Starting thumbnail extraction (simulating ModDashboard)...');
        
        for (const mod of mods) {
            console.log(`\n🔍 Processing mod: ${mod.fileName}, has filePath: ${!!mod.filePath}`);
            
            if (mod.filePath) {
                try {
                    const buffer = fs.readFileSync(mod.filePath);
                    console.log(`📏 File size: ${(buffer.length / 1024).toFixed(2)} KB`);
                    
                    const result = await ThumbnailExtractionService.extractThumbnails(
                        buffer,
                        mod.fileName,
                        {
                            maxThumbnails: 1,
                            preferredFormat: 'webp',
                            maxWidth: 256,
                            maxHeight: 256,
                            prioritizeCasThumbnails: true,
                            generateFallbacks: true
                        }
                    );
                    
                    if (result.success && result.thumbnails.length > 0) {
                        const thumbnail = result.thumbnails[0];
                        console.log(`✅ Successfully extracted thumbnail for ${mod.fileName}, data length: ${thumbnail.imageData.length}`);
                        console.log(`   Resource type: ${thumbnail.resourceType}, Method: ${thumbnail.extractionMethod}`);
                        console.log(`   Data preview: ${thumbnail.imageData.substring(0, 50)}...`);
                        
                        // Simulate setting the thumbnail URL like the UI does
                        mod.thumbnailUrl = thumbnail.imageData;
                        mod.thumbnailData = thumbnail.imageData;
                        
                        console.log(`   ✅ Mod object updated with thumbnailUrl (length: ${mod.thumbnailUrl.length})`);
                    } else {
                        console.log(`⚠️ No thumbnails extracted for ${mod.fileName}, using fallback`);
                        console.log(`   Errors: ${result.errors.join(', ')}`);
                        
                        // Create fallback like the UI does
                        mod.thumbnailUrl = createMockThumbnailData(mod);
                        mod.thumbnailData = mod.thumbnailUrl;
                        console.log(`   📦 Created fallback thumbnail (length: ${mod.thumbnailUrl.length})`);
                    }
                } catch (fileError) {
                    console.error(`❌ Failed to extract thumbnail for ${mod.fileName}:`, fileError.message);
                }
            } else {
                console.log(`❌ No file path for ${mod.fileName}`);
            }
        }
        
        // Summary
        console.log('\n📊 Final Results:');
        mods.forEach(mod => {
            const hasThumb = !!mod.thumbnailUrl;
            const isReal = mod.thumbnailUrl?.startsWith('data:image/png;base64,/9j') || mod.thumbnailUrl?.startsWith('data:image/webp');
            const isFallback = mod.thumbnailUrl?.startsWith('data:image/svg+xml');
            
            console.log(`  ${mod.fileName}: ${hasThumb ? '✅' : '❌'} ${isReal ? 'REAL' : isFallback ? 'FALLBACK' : 'NONE'}`);
        });
        
    } catch (error) {
        console.error('❌ Test failed:', error);
    }
}

function findCCFiles(directory: string): string[] {
    const ccFiles: string[] = [];
    
    try {
        const items = fs.readdirSync(directory);
        
        for (const item of items) {
            const fullPath = path.join(directory, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                ccFiles.push(...findCCFiles(fullPath));
            } else if (item.toLowerCase().endsWith('.package')) {
                const fileName = item.toLowerCase();
                if (fileName.includes('aurum') || 
                    fileName.includes('felixandre') || 
                    fileName.includes('chateau') ||
                    fileName.includes('hair') ||
                    fileName.includes('cas') ||
                    fileName.includes('clothing')) {
                    ccFiles.push(fullPath);
                }
            }
        }
    } catch (error) {
        console.warn(`⚠️ Could not read directory ${directory}:`, error.message);
    }
    
    return ccFiles;
}

function createMockThumbnailData(mod: MockMod): string {
    const category = mod.category || 'unknown';
    
    const svg = `<svg width="256" height="256" xmlns="http://www.w3.org/2000/svg">
        <rect width="256" height="256" fill="#f3f4f6"/>
        <text x="128" y="128" text-anchor="middle" font-family="Arial" font-size="14" fill="#6b7280">
            ${category.toUpperCase()}
        </text>
    </svg>`;
    
    return `data:image/svg+xml;base64,${btoa(svg)}`;
}

// Run the test
if (require.main === module) {
    testUIThumbnailIntegration();
}
