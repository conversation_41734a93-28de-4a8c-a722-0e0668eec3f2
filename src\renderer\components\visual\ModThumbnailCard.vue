<template>
  <div :class="['mod-thumbnail-card', viewMode, { selected: isSelected }]" @click="handleClick">
    <!-- Thumbnail Section -->
    <div class="thumbnail-container">
      <div v-if="thumbnailLoading" class="thumbnail-loading">
        <div class="loading-spinner"></div>
      </div>
      
      <img
        v-else-if="thumbnailUrl"
        :src="thumbnailUrl"
        :alt="`${mod.fileName} thumbnail`"
        class="thumbnail-image"
        @load="handleThumbnailLoad"
        @error="handleThumbnailError"
      />
      
      <div v-else class="thumbnail-placeholder">
        <component :is="getPlaceholderIcon()" class="placeholder-icon" />
        <span class="placeholder-text">{{ getPlaceholderText() }}</span>
      </div>
      
      <!-- Overlay badges -->
      <div class="thumbnail-overlay">
        <div class="badge-container">
          <span v-if="mod.hasConflicts" class="badge badge-warning" title="Has conflicts">
            <ExclamationTriangleIcon class="w-3 h-3" />
          </span>
          <span v-if="mod.isCorrupted" class="badge badge-error" title="Corrupted file">
            <XCircleIcon class="w-3 h-3" />
          </span>
          <span v-if="isHighQuality" class="badge badge-success" title="High quality">
            <SparklesIcon class="w-3 h-3" />
          </span>
        </div>
        
        <div class="action-buttons">
          <button
            v-if="viewMode === 'grid'"
            @click.stop="toggleFavorite"
            :class="['action-btn', { active: isFavorite }]"
            title="Add to favorites"
          >
            <HeartIcon class="w-4 h-4" />
          </button>
          <button
            @click.stop="showQuickActions = !showQuickActions"
            class="action-btn"
            title="Quick actions"
          >
            <EllipsisVerticalIcon class="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <!-- Quick Actions Menu -->
      <div v-if="showQuickActions" class="quick-actions-menu" @click.stop>
        <button @click="openInExplorer" class="quick-action">
          <FolderOpenIcon class="w-4 h-4" />
          Open in Explorer
        </button>
        <button @click="copyPath" class="quick-action">
          <ClipboardIcon class="w-4 h-4" />
          Copy Path
        </button>
        <button @click="showDetails" class="quick-action">
          <InformationCircleIcon class="w-4 h-4" />
          View Details
        </button>
        <button v-if="mod.isCorrupted" @click="deleteMod" class="quick-action danger">
          <TrashIcon class="w-4 h-4" />
          Delete
        </button>
      </div>
    </div>
    
    <!-- Content Section -->
    <div class="content-section">
      <div class="mod-header">
        <h3 class="mod-title" :title="mod.fileName">{{ displayName }}</h3>
        <div class="mod-meta">
          <span class="category-badge" :class="getCategoryClass()">
            {{ mod.category || 'Unknown' }}
          </span>
          <span class="file-size">{{ formatFileSize(mod.fileSize) }}</span>
        </div>
      </div>
      
      <div v-if="viewMode === 'list'" class="mod-details">
        <p class="mod-description">{{ mod.description || 'No description available' }}</p>
        <div class="mod-stats">
          <div class="stat-item">
            <span class="stat-label">Resources:</span>
            <span class="stat-value">{{ mod.resourceCount || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Last Modified:</span>
            <span class="stat-value">{{ formatDate(mod.lastModified) }}</span>
          </div>
          <div v-if="mod.author" class="stat-item">
            <span class="stat-label">Author:</span>
            <span class="stat-value">{{ mod.author }}</span>
          </div>
        </div>
      </div>
      
      <!-- Enhanced Content Analysis Display -->
      <div v-if="hasEnhancedContent" class="enhanced-content">
        <div class="content-tags">
          <span
            v-for="tag in getContentTags()"
            :key="tag"
            class="content-tag"
          >
            {{ tag }}
          </span>
        </div>
        
        <div v-if="mod.objectClassification" class="object-info">
          <span class="object-type">{{ mod.objectClassification.specificType }}</span>
          <span v-if="mod.objectClassification.roomAssignment" class="room-assignment">
            for {{ mod.objectClassification.roomAssignment }}
          </span>
        </div>
      </div>
      
      <!-- Progress bar for analysis -->
      <div v-if="isAnalyzing" class="analysis-progress">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${analysisProgress}%` }"></div>
        </div>
        <span class="progress-text">Analyzing... {{ analysisProgress }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import {
  ExclamationTriangleIcon,
  XCircleIcon,
  SparklesIcon,
  HeartIcon,
  EllipsisVerticalIcon,
  FolderOpenIcon,
  ClipboardIcon,
  InformationCircleIcon,
  TrashIcon,
  DocumentIcon,
  UserIcon,
  HomeIcon,
  CogIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';

import type { ModData } from '../../../types/ModData';
import { ThumbnailExtractionService } from '../../../services/visual/ThumbnailExtractionService';

// Props
interface Props {
  mod: ModData;
  viewMode: 'grid' | 'list';
  isSelected?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false
});

// Emits
const emit = defineEmits<{
  click: [mod: ModData];
  thumbnailError: [modId: string];
  favorite: [modId: string, isFavorite: boolean];
  delete: [modId: string];
}>();

// Reactive state
const thumbnailUrl = ref<string | null>(null);
const thumbnailLoading = ref(false);
const showQuickActions = ref(false);
const isFavorite = ref(false);
const isAnalyzing = ref(false);
const analysisProgress = ref(0);

// Computed properties
const displayName = computed(() => {
  const name = props.mod.fileName;
  // Remove file extension and clean up the name
  return name.replace(/\.(package|ts4script)$/i, '').replace(/_/g, ' ');
});

const isHighQuality = computed(() => {
  // Determine if this is a high-quality mod based on various factors
  const hasHighResources = (props.mod.resourceCount || 0) > 50;
  const hasLargeSize = (props.mod.fileSize || 0) > 1024 * 1024; // > 1MB
  const hasEnhancedAnalysis = props.mod.objectClassification?.confidence > 0.8;
  
  return hasHighResources || hasLargeSize || hasEnhancedAnalysis;
});

const hasEnhancedContent = computed(() => {
  return props.mod.objectClassification || props.mod.universalClassification;
});

// Methods
const handleClick = () => {
  emit('click', props.mod);
};

const handleThumbnailLoad = () => {
  thumbnailLoading.value = false;
};

const handleThumbnailError = () => {
  thumbnailLoading.value = false;
  thumbnailUrl.value = null;
  emit('thumbnailError', props.mod.id);
};

const toggleFavorite = () => {
  isFavorite.value = !isFavorite.value;
  emit('favorite', props.mod.id, isFavorite.value);
};

const openInExplorer = () => {
  // Emit event to parent to handle file explorer opening
  window.electronAPI?.openInExplorer?.(props.mod.filePath);
  showQuickActions.value = false;
};

const copyPath = async () => {
  try {
    await navigator.clipboard.writeText(props.mod.filePath);
    // Could show a toast notification here
  } catch (error) {
    console.error('Failed to copy path:', error);
  }
  showQuickActions.value = false;
};

const showDetails = () => {
  emit('click', props.mod);
  showQuickActions.value = false;
};

const deleteMod = () => {
  if (confirm(`Are you sure you want to delete "${props.mod.fileName}"?`)) {
    emit('delete', props.mod.id);
  }
  showQuickActions.value = false;
};

const getPlaceholderIcon = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return UserIcon;
  if (category.includes('build') || category.includes('buy')) return HomeIcon;
  if (category.includes('script')) return CogIcon;
  if (category.includes('gameplay')) return PuzzlePieceIcon;
  
  return DocumentIcon;
};

const getPlaceholderText = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas')) return 'CAS';
  if (category.includes('build') || category.includes('buy')) return 'Build/Buy';
  if (category.includes('script')) return 'Script';
  if (category.includes('gameplay')) return 'Gameplay';
  
  return 'Mod';
};

const getCategoryClass = () => {
  const category = props.mod.category?.toLowerCase() || '';
  
  if (category.includes('cas') || category.includes('create-a-sim')) return 'category-cas';
  if (category.includes('build') || category.includes('buy')) return 'category-build';
  if (category.includes('script')) return 'category-script';
  if (category.includes('gameplay')) return 'category-gameplay';
  
  return 'category-other';
};

const getContentTags = (): string[] => {
  const tags: string[] = [];
  
  if (props.mod.objectClassification) {
    if (props.mod.objectClassification.category) {
      tags.push(props.mod.objectClassification.category);
    }
    if (props.mod.objectClassification.functionality) {
      tags.push(...props.mod.objectClassification.functionality);
    }
  }
  
  if (props.mod.universalClassification) {
    if (props.mod.universalClassification.subcategory) {
      tags.push(props.mod.universalClassification.subcategory);
    }
    if (props.mod.universalClassification.ageGroups) {
      tags.push(...props.mod.universalClassification.ageGroups);
    }
  }
  
  return tags.slice(0, 3); // Limit to 3 tags for display
};

const formatFileSize = (bytes: number | undefined): string => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

const formatDate = (timestamp: number | undefined): string => {
  if (!timestamp) return 'Unknown';
  
  const date = new Date(timestamp);
  return date.toLocaleDateString();
};

const loadThumbnail = async () => {
  if (!props.mod.filePath) return;
  
  thumbnailLoading.value = true;
  
  try {
    // Check if we already have thumbnail data
    if (props.mod.thumbnailData) {
      thumbnailUrl.value = props.mod.thumbnailData;
      thumbnailLoading.value = false;
      return;
    }
    
    // Extract thumbnail from the mod file
    const fs = await import('fs');
    const buffer = fs.readFileSync(props.mod.filePath);
    
    const result = await ThumbnailExtractionService.extractThumbnails(
      buffer,
      props.mod.fileName,
      {
        maxThumbnails: 1,
        preferredFormat: 'webp',
        maxWidth: 256,
        maxHeight: 256,
        prioritizeCasThumbnails: true
      }
    );
    
    if (result.success && result.thumbnails.length > 0) {
      thumbnailUrl.value = result.thumbnails[0].imageData;
    }
    
  } catch (error) {
    console.error('Failed to load thumbnail:', error);
    handleThumbnailError();
  } finally {
    thumbnailLoading.value = false;
  }
};

// Lifecycle
onMounted(() => {
  loadThumbnail();
});

// Close quick actions when clicking outside
const handleClickOutside = (event: Event) => {
  if (!event.target || !(event.target as Element).closest('.quick-actions-menu')) {
    showQuickActions.value = false;
  }
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style scoped>
.mod-thumbnail-card {
  @apply bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700
         hover:shadow-md hover:border-gray-300 dark:hover:border-gray-600
         transition-all duration-200 cursor-pointer overflow-hidden;
}

.mod-thumbnail-card.selected {
  @apply ring-2 ring-green-500 border-green-500;
}

.mod-thumbnail-card.list {
  @apply flex items-center p-4 space-x-4;
}

.mod-thumbnail-card.grid {
  @apply flex flex-col;
}

/* Thumbnail Styles */
.thumbnail-container {
  @apply relative;
}

.mod-thumbnail-card.grid .thumbnail-container {
  @apply aspect-square;
}

.mod-thumbnail-card.list .thumbnail-container {
  @apply w-16 h-16 flex-shrink-0;
}

.thumbnail-loading {
  @apply w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-700;
}

.loading-spinner {
  @apply w-6 h-6 border-2 border-gray-300 border-t-green-500 rounded-full animate-spin;
}

.thumbnail-image {
  @apply w-full h-full object-cover;
}

.thumbnail-placeholder {
  @apply w-full h-full flex flex-col items-center justify-center 
         bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800
         text-gray-500 dark:text-gray-400;
}

.placeholder-icon {
  @apply w-8 h-8 mb-2;
}

.mod-thumbnail-card.list .placeholder-icon {
  @apply w-6 h-6 mb-1;
}

.placeholder-text {
  @apply text-sm font-medium;
}

.mod-thumbnail-card.list .placeholder-text {
  @apply text-xs;
}

/* Overlay Styles */
.thumbnail-overlay {
  @apply absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 
         transition-all duration-200 flex flex-col justify-between p-2;
}

.badge-container {
  @apply flex space-x-1;
}

.badge {
  @apply flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.badge-warning {
  @apply bg-yellow-500 text-white;
}

.badge-error {
  @apply bg-red-500 text-white;
}

.badge-success {
  @apply bg-green-500 text-white;
}

.action-buttons {
  @apply flex space-x-1 self-end opacity-0 group-hover:opacity-100 transition-opacity;
}

.mod-thumbnail-card:hover .action-buttons {
  @apply opacity-100;
}

.action-btn {
  @apply p-1.5 bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300
         rounded-full shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700
         transition-colors;
}

.action-btn.active {
  @apply text-red-500;
}

/* Quick Actions Menu */
.quick-actions-menu {
  @apply absolute top-full right-0 mt-1 bg-white dark:bg-gray-800 
         border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg
         py-1 z-10 min-w-40;
}

.quick-action {
  @apply w-full flex items-center space-x-2 px-3 py-2 text-sm
         text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700
         transition-colors;
}

.quick-action.danger {
  @apply text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900;
}

/* Content Styles */
.content-section {
  @apply p-4;
}

.mod-thumbnail-card.list .content-section {
  @apply flex-1 p-0;
}

.mod-header {
  @apply mb-2;
}

.mod-title {
  @apply text-lg font-semibold text-gray-900 dark:text-white truncate mb-1;
}

.mod-thumbnail-card.list .mod-title {
  @apply text-base;
}

.mod-meta {
  @apply flex items-center space-x-2;
}

.category-badge {
  @apply px-2 py-1 rounded-full text-xs font-medium;
}

.category-cas {
  @apply bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200;
}

.category-build {
  @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

.category-script {
  @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.category-gameplay {
  @apply bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200;
}

.category-other {
  @apply bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200;
}

.file-size {
  @apply text-xs text-gray-500 dark:text-gray-400;
}

/* List View Details */
.mod-details {
  @apply mt-2;
}

.mod-description {
  @apply text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2;
}

.mod-stats {
  @apply flex space-x-4 text-xs text-gray-500 dark:text-gray-400;
}

.stat-item {
  @apply flex space-x-1;
}

.stat-label {
  @apply font-medium;
}

/* Enhanced Content */
.enhanced-content {
  @apply mt-2 space-y-2;
}

.content-tags {
  @apply flex flex-wrap gap-1;
}

.content-tag {
  @apply px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
         text-xs rounded-full;
}

.object-info {
  @apply text-sm text-gray-600 dark:text-gray-400;
}

.object-type {
  @apply font-medium;
}

.room-assignment {
  @apply text-gray-500 dark:text-gray-500;
}

/* Analysis Progress */
.analysis-progress {
  @apply mt-2;
}

.progress-bar {
  @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-1;
}

.progress-fill {
  @apply bg-green-500 h-2 rounded-full transition-all duration-300;
}

.progress-text {
  @apply text-xs text-gray-500 dark:text-gray-400;
}
</style>
